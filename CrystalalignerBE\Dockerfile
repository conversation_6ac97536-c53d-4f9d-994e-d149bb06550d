# Build aşaması
FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build
WORKDIR /src

COPY Crystalaligner.sln ./

COPY src/Services/Management/Crystalaligner/*.csproj ./src/Services/Management/Crystalaligner/
COPY src/Services/Management/Crystalaligner.Application/*.csproj ./src/Services/Management/Crystalaligner.Application/
COPY src/Services/Management/Crystalaligner.Domain/*.csproj ./src/Services/Management/Crystalaligner.Domain/
COPY src/Services/Management/Crystalaligner.Infrastructure/*.csproj ./src/Services/Management/Crystalaligner.Infrastructure/
COPY src/Services/Management/Crystalaligner.Model/*.csproj ./src/Services/Management/Crystalaligner.Model/
COPY src/Services/Shared/Crystalaligner.Core/*.csproj ./src/Services/Shared/Crystalaligner.Core/
COPY src/Services/Shared/Crystalaligner.Helper/*.csproj ./src/Services/Shared/Crystalaligner.Helper/

RUN dotnet restore "src/Services/Management/Crystalaligner/Crystalaligner.csproj"

COPY . .

WORKDIR /src/src/Services/Management/Crystalaligner
RUN dotnet publish "Crystalaligner.csproj" -c Release -o /app/publish --no-restore

FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS final
WORKDIR /app
COPY --from=build /app/publish .
ENV ASPNETCORE_URLS=http://+:5000
EXPOSE 5000
ENTRYPOINT ["dotnet", "Crystalaligner.dll"]