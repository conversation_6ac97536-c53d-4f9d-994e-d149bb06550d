import type { Routes } from '@/@types/routes'
import { lazy } from 'react'
import authRoute from './authRoute'
import path from 'path'

export const publicRoutes: Routes = [...authRoute]

export const protectedRoutes = [
    {
        key: 'home',
        path: '/home',
        component: lazy(() => import('@/views/Home')),
        authority: [],
        meta: {
            icon: 'HomeIcon',
            title: 'Home',
            showInSidebar: true,
        },
    },
    {
        key: 'admins',
        path: '/admins',
        component: lazy(() => import('@/views/admins/Admins')),
        authority: ['admin'],
    },
    {
        key: 'profile',
        path: '/profile',
        component: lazy(() => import('@/views/profile/MyProfile')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'orders',
        path: '/orders',
        component: lazy(() => import('@/views/orders/Orders')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'refinements',
        path: '/refinements',
        component: lazy(() => import('@/views/refinements/Refinements')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'orders',
        path: '/orders/:id',
        component: lazy(() => import('@/views/orders/OrderDetails')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'refinements',
        path: '/refinements/:id',
        component: lazy(() => import('@/views/refinements/RefinementDetails')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'packages',
        path: '/packages',
        component: lazy(() => import('@/views/packages/Packages')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'user-packages',
        path: '/packages/user-packages',
        component: lazy(() => import('@/views/packages/UserPackages')),
        authority: ['doctor'],
    },
    {
        key: 'packages-purchase',
        path: '/packages/purchase',
        component: lazy(() => import('@/views/packages/Purchase')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'payment-partial',
        path: '/packages/purchase/payment-partial',
        component: lazy(() => import('@/views/packages/PaymentPartial')),
        authority: ['doctor'],
    },
    {
        key: 'payment-success',
        path: '/packages/purchase/payment-success',
        component: lazy(() => import('@/views/packages/PaymentSuccess')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'payment-failed',
        path: '/packages/purchase/payment-failed',
        component: lazy(() => import('@/views/packages/PaymentFailed')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },

    {
        key: 'treatment-plan',
        path: '/orders/:id/treatment-plan/:treatmentPlanId',
        component: lazy(
            () => import('@/views/orders/treatment-plan/TreatmentPlan')
        ),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'order-add',
        path: '/order-add',
        component: lazy(() => import('@/views/orders/OrderAdd')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'refinements',
        path: '/refinement-add',
        component: lazy(() => import('@/views/refinements/RefinementAdd')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor'],
    },
    {
        key: 'admins-details',
        path: '/admins/:id',
        component: lazy(() => import('@/views/admins/AdminDetails')),
        authority: ['admin'],
    },
    {
        key: 'doctors',
        path: '/doctors',
        component: lazy(() => import('@/views/doctors/Doctors')),
        authority: ['admin', 'chief_agent', 'agent'],
    },
    {
        key: 'doctors-details',
        path: '/doctors/:id',
        component: lazy(() => import('@/views/doctors/DoctorDetails')),
        authority: ['admin', 'chief_agent', 'agent'],
    },
    {
        key: 'doctorCandidates',
        path: '/doctor-candidates',
        component: lazy(() => import('@/views/doctors/DoctorCandidates')),
        authority: ['admin'],
    },
    {
        key: 'agents',
        path: '/agents',
        component: lazy(() => import('@/views/agents/Agents')),
        authority: ['admin', 'chief_agent'],
    },
    {
        key: 'agents-details',
        path: '/agents/:id',
        component: lazy(() => import('@/views/agents/AgentDetails')),
        authority: ['admin', 'chief_agent'],
    },
    {
        key: 'technicians',
        path: '/technicians',
        component: lazy(() => import('@/views/technicians/Technicians')),
        authority: ['admin', 'chief_agent'],
    },
    {
        key: 'technicians-details',
        path: '/technicians/:id',
        component: lazy(() => import('@/views/technicians/TechnicianDetails')),
        authority: ['admin', 'chief_agent'],
    },
    {
        key: 'addUser',
        path: '/add-user',
        component: lazy(() => import('@/views/AddUser')),
        authority: ['admin', 'chief_agent'],
    },
    {
        key: 'market',
        path: '/market',
        component: lazy(() => import('@/views/market/Market')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor', 'technician'],
    },
    ///market/colgate-visible-white-maksimum-beyazlik-beyazlatici-dis-macunu-75-ml-x-4-adet&id=7
    {
        key: 'product-details',
        path: '/market/:name/:id',
        component: lazy(() => import('@/views/market/ProductDetails')),
        authority: ['admin', 'chief_agent', 'agent', 'doctor', 'technician'],
    },
    {
        key: 'purchase-order-info',
        path: '/market/purchase-order-info',
        component: lazy(() => import('@/views/market/PurchaseOrderInfo')),
        authority: ['admin', 'doctor'],
    },
    {
        key: 'market',
        path: '/market/basket',
        component: lazy(() => import('@/views/market/MarketBasket')),
        authority: ['admin', 'doctor'],
    },
    {
        key: 'market',
        path: '/market/shopping-history',
        component: lazy(() => import('@/views/market/ShoppingHistory')),
        authority: ['admin', 'doctor'],
    },
    {
        key: 'market',
        path: '/market/shopping-history/:id',
        component: lazy(() => import('@/views/market/ShoppingHistoryDetails')),
        authority: ['admin', 'doctor'],
    },
    {
        key: 'market',
        path: '/market/shopping-detail-history',
        component: lazy(() => import('@/views/market/ShoppingDetailHistory')),
        authority: ['admin', 'doctor'],
    },
    {
        key: 'market',
        path: '/market/payment/payment-success',
        component: lazy(
            () => import('@/views/market/payment/MarketPaymentSuccess')
        ),
        authority: ['admin', 'doctor'],
    },
    {
        key: 'market',
        path: '/market/payment/payment-failed',
        component: lazy(
            () => import('@/views/market/payment/MarketPaymentFailed')
        ),
        authority: ['admin', 'doctor'],
    },
    {
        key: 'landing-page-faq',
        path: '/landing-page/faq',
        component: lazy(() => import('@/views/landing-page/FaqManagement')),
        authority: ['admin'],
    },
    {
        key: 'landing-page-about-us',
        path: '/landing-page/about-us',
        component: lazy(() => import('@/views/landing-page/AboutUsManagement')),
        authority: ['admin'],
    },
    {
        key: 'landing-page-first-case',
        path: '/landing-page/first-case',
        component: lazy(
            () => import('@/views/landing-page/FirstCaseManagement')
        ),
        authority: ['admin'],
    },
    {
        key: 'landing-page-question-management',
        path: '/landing-page/question-management',
        component: lazy(
            () => import('@/views/landing-page/QuestionManagement')
        ),
        authority: ['admin'],
    },
    {
        key: 'landing-page-gallery-management',
        path: '/landing-page/gallery-management',
        component: lazy(() => import('@/views/landing-page/GalleryManagement')),
        authority: ['admin'],
    },
    {
        key: 'landing-page-statistics-management',
        path: '/landing-page/statistics-management',
        component: lazy(
            () => import('@/views/landing-page/StatisticsManagement')
        ),
        authority: ['admin'],
    },
    {
        key: 'how-it-work-management',
        path: '/landing-page/how-it-work-management',
        component: lazy(
            () => import('@/views/landing-page/HowItWorkManagement')
        ),
        authority: ['admin'],
    },
    {
        key: 'landing-page-testimonial-management',
        path: '/landing-page/testimonial-management',
        component: lazy(
            () => import('@/views/landing-page/TestimonialManagement')
        ),
        authority: ['admin'],
    },
    {
        key: 'landing-page-who-we-are',
        path: '/landing-page/who-we-are',
        component: lazy(
            () => import('@/views/landing-page/WhoWeaAreManagement')
        ),
        authority: ['admin'],
    },
    {
        key: 'landing-page/treatment-management',
        path: '/landing-page/treatment-management',
        component: lazy(
            () => import('@/views/landing-page/TreatmentManagement')
        ),
        authority: ['admin'],
    },
    {
        key: 'landing-page/footer-management',
        path: '/landing-page/footer-management',
        component: lazy(() => import('@/views/landing-page/FooterManagement')),
        authority: ['admin'],
    },
    {
        key: 'landing-page/advantages-management',
        path: '/landing-page/advantages-management',
        component: lazy(
            () => import('@/views/landing-page/AdvantagesManagement')
        ),
        authority: ['admin'],
    },
    {
        key: 'landing-page/hero-management',
        path: '/landing-page/hero-management',
        component: lazy(() => import('@/views/landing-page/HeroManagement')),
        authority: ['admin'],
    },
    {
        key: 'landing-page/privacy-policy-management',
        path: '/landing-page/privacy-policy-management',
        component: lazy(
            () => import('@/views/landing-page/PrivacyPolicyManagement')
        ),
        authority: ['admin'],
    },
    {
        key: 'doctor-adress-management',
        path: '/doctor-adress-management',
        component: lazy(
            () => import('@/views/landing-page/DoctorAddressManagement')
        ),
        authority: ['admin', 'chief_agent', 'agent'],
    },
    {
        key: 'cargo-management',
        path: '/cargo-management',
        component: lazy(() => import('@/views/Cargo/CargoManagement')),
        authority: ['admin'],
    },
    {
        key: 'demo-upload-test',
        path: '/demo/upload-test',
        component: lazy(() => import('@/views/demo/UploadTestView')),
        authority: ['admin'],
        meta: {
            icon: 'UploadIcon',
            title: 'Upload Test',
            showInSidebar: true,
        },
    },
]
