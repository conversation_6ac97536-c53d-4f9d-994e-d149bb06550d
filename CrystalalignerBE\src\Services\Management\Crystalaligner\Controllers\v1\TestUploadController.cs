using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Crystalaligner.Controllers.v1
{
    [ApiController]
    [Route("api/test-upload")]
    public class TestUploadController : ControllerBase
    {
        private readonly string _uploadPath = "/mnt/storagebox/test-uploads";

        [HttpPost]
        [RequestSizeLimit(524288000)] // 500MB
        public async Task<IActionResult> Upload(List<IFormFile> files)
        {
            if (!Directory.Exists(_uploadPath))
                Directory.CreateDirectory(_uploadPath);

            var uploaded = new List<string>();
            foreach (var file in files)
            {
                if (file.Length > 0)
                {
                    var filePath = Path.Combine(_uploadPath, Path.GetFileName(file.FileName));
                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }
                    uploaded.Add(file.FileName);
                }
            }
            return Ok(new { uploaded });
        }

        [HttpGet("list")]
        public IActionResult List()
        {
            if (!Directory.Exists(_uploadPath))
                return Ok(new List<string>());
            var files = Directory.GetFiles(_uploadPath).Select(Path.GetFileName).ToList();
            return Ok(files);
        }
    }
}
