import React, { useState, useEffect } from 'react'
import Upload from '../../components/ui/Upload/Upload'
import Button from '../../components/ui/Button/Button'

const API_URL = '/api/test-upload'

const UploadTestView: React.FC = () => {
    const [selectedFiles, setSelectedFiles] = useState<File[]>([])
    const [uploadedFiles, setUploadedFiles] = useState<string[]>([])
    const [uploading, setUploading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const fetchFiles = async () => {
        try {
            const res = await fetch(`${API_URL}/list`)
            const data = await res.json()
            setUploadedFiles(data)
        } catch (err) {
            setError('Dosya listesi alınamadı.')
        }
    }

    useEffect(() => {
        fetchFiles()
    }, [])

    const handleUpload = async () => {
        if (selectedFiles.length === 0) return
        setUploading(true)
        setError(null)
        const formData = new FormData()
        selectedFiles.forEach((file) => formData.append('files', file))
        try {
            const res = await fetch(API_URL, {
                method: 'POST',
                body: formData,
            })
            if (!res.ok) throw new Error('Yükleme başarısız')
            await fetchFiles()
            setSelectedFiles([])
        } catch (err) {
            setError('Yükleme başarısız.')
        } finally {
            setUploading(false)
        }
    }

    return (
        <div
            style={{
                maxWidth: 600,
                margin: '40px auto',
                padding: 24,
                background: '#fff',
                borderRadius: 8,
            }}
        >
            <h2>StorageBox Test Upload</h2>
            <Upload
                multiple
                fileList={selectedFiles}
                onChange={setSelectedFiles}
                showList
            />
            <Button
                onClick={handleUpload}
                disabled={uploading || selectedFiles.length === 0}
                style={{ marginTop: 16 }}
            >
                {uploading ? 'Yükleniyor...' : 'Yükle'}
            </Button>
            {error && <div style={{ color: 'red', marginTop: 8 }}>{error}</div>}
            <h3 style={{ marginTop: 32 }}>Yüklenen Dosyalar</h3>
            <ul>
                {uploadedFiles.map((file) => (
                    <li key={file}>{file}</li>
                ))}
            </ul>
        </div>
    )
}

export default UploadTestView
